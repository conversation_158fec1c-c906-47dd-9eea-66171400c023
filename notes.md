
```json
{
    “setup”: {
    “model”: “models/gemini-2.0-flash-live-001”,
        “generationConfig”: {
            “responseModalities”: [“AUDIO”],
        },
        “systemInstruction”: {
            “parts”: [
                {“text”: greetingPrompt}
            ]
        },
        “realtimeInputConfig”: {
            “automaticActivityDetection”: {“disabled”: true}
        },
        “outputAudioTranscription”: {}
    }
}

    //and while receiving, you can find it in servercontent object -
{
    “serverContent”:{
        “outputTranscription”:{ “text” : “your transcript”}
    }
}   
```