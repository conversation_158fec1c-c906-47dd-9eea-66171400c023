"use client"

import * as React from "react"
import { useState } from "react"
import { Mic, Send, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { useGeminiLive } from "@/hooks/use-gemini-live"

export function CTASection() {
  const [email, setEmail] = useState("")
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [showVoiceAgent, setShowVoiceAgent] = useState(false)
  const {
    isRecording,
    status,
    error,
    isConnected,
    startRecording,
    stopRecording,
    initClient
  } = useGeminiLive()

  // Initialize the client when component mounts
  React.useEffect(() => {
    initClient()
  }, [initClient])

  const handleMicClick = async () => {
    console.log('🎤 Mic button clicked, isRecording:', isRecording);

    if (isRecording) {
      console.log('🛑 Stopping recording...');
      stopRecording();
      setShowVoiceAgent(false);
    } else {
      console.log('🎤 Starting recording...');
      setShowVoiceAgent(true);
      try {
        // Custom initial message to trigger Mae to start talking immediately
        const initialMessage = "Hi Mae! I just clicked the microphone button and I'm ready to talk with you about Our Kids platform. Please introduce yourself and tell me how you can help.";
        
        await startRecording(initialMessage);
        console.log('✅ Recording started successfully with custom system instruction');
      } catch (error) {
        console.error('❌ Failed to start recording:', error);
        setShowVoiceAgent(false);
      }
    }
  }

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (email) {
      setIsSubmitted(true)
      // Reset after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false)
        setEmail("")
      }, 3000)
    }
  }

  return (
    <section className="py-24  ">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12 font-inter font-light gradient-to-r from-teal-500 to-blue-400 space-y-4 text-white">
            <Badge variant="outline" className="mb-4 w-fit h-8 mx-auto px-10 text-md bg-gradient-to-r from-teal-500 to-blue-400 text-white font-inter font-medium">
              Try It Now
            </Badge>
            <h2 className="text-2xl md:text-3xl font-inter font-light tracking-tight">
              Experience AI-Powered Pediatric Care
            </h2>
            <p className="text-muted-foreground font-inter font-light max-w-3xl mx-auto leading-relaxed mb-10">
              Just speak your question and get AI-powered answers in seconds. 
              No appointment needed.
            </p>
          </div>

          {/* Voice Demo */}
          <Card className="mb-10 healthcare-card hover:border-2 hover:border-teal-500">
            <CardHeader className="text-center font-inter font-tight font-light">
              <CardTitle className="text-xl font-inter font-light">Try it now — just speak your question</CardTitle>
              <CardDescription className="font-inter font-light">
                Ask anything about your child's health and get instant guidance
              </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center space-y-6">
              <div className="relative flex flex-col items-center space-y-4">
                {/* Voice Agent Interface */}
                {showVoiceAgent && (
                  <div className="w-full max-w-md p-6 bg-card dark:bg-card rounded-xl ">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-teal-800 dark:text-teal-200"></h3>
                      <button
                        onClick={() => {
                          setShowVoiceAgent(false)
                          stopRecording()
                        }}
                        className="w-8 h-8 bg-red-500 text-white rounded-full text-sm flex items-center justify-center transition-all shadow-lg"
                      >
                        ×
                      </button>
                    </div>

                    {/* Audio visualization */}
                    <div className="flex items-center justify-center mb-4">
                      <div className={`w-16 h-16 rounded-full border-4 border-teal-400 flex items-center justify-center ${isRecording ? 'animate-pulse bg-teal-100' : 'bg-gray-100'}`}>
                        <Mic className={`w-8 h-8 ${isRecording ? 'text-teal-600' : 'text-gray-400'}`} />
                      </div>
                    </div>

                    <div className="text-center">
                      <p className="text-sm text-teal-700 mb-2">
                        {isRecording ? "🎤 Listening... Speak your question" : isConnected ? "Ready to listen" : "Connecting..."}
                      </p>
                      <Button
                        onClick={handleMicClick}
                        disabled={!isConnected}
                        className={`${isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-teal-500 hover:bg-teal-600'} text-white disabled:opacity-50 disabled:cursor-not-allowed`}
                      >
                        {isRecording ? 'Stop Recording' : 'Start Recording'}
                      </Button>
                    </div>
                  </div>
                )}

                {/* Main Voice Button */}
                {!showVoiceAgent && (
                  <Button
                    size="icon"
                    onClick={handleMicClick}
                    className="relative z-30 size-16 rounded-full transition-all cursor-pointer duration-300 border-2 border-teal-500 bg-primary hover:bg-primary/90 hover:scale-105"
                  >
                    <Mic className="text-primary-foreground w-16 h-16 sm:w-20 sm:h-20" style={{ width: "2rem", height: "2rem" }} />
                  </Button>
                )}
              </div>
              <div className="text-center space-y-2">
                {/* <p className="text-sm text-muted-foreground">
                  {isRecording ? "🎤 Listening..." : "Click to start voice demo"}
                </p> */}
                {/* {status && (
                  <p className="text-xs text-muted-foreground bg-blue-50 p-2 rounded">
                    Status: {status}
                  </p>
                )} */}
                {error && (
                  <p className="text-xs text-red-500 bg-red-50 p-2 rounded">
                    Error: {error}
                  </p>
                )}
                {!showVoiceAgent && (
                  <p className="text-sm font-inter font-light text-muted-foreground">
                    Experience our AI-powered pediatric voice assistant
                  </p>
                )}

                {/* Debug info */}
                {/* {process.env.NODE_ENV === 'development' && (
                  <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded mt-2 space-y-1">
                    <div>Connected: {isConnected ? 'Yes' : 'No'}</div>
                    <div>Recording: {isRecording ? 'Yes' : 'No'}</div>
                    <div>Voice Agent: {showVoiceAgent ? 'Shown' : 'Hidden'}</div>
                    <div>API Key: {process.env.NEXT_PUBLIC_GEMINI_API_KEY ? 'Set' : 'Missing'}</div>
                    <button
                      onClick={initClient}
                      className="bg-blue-500 text-white px-2 py-1 rounded text-xs mt-1"
                    >
                      Test Init Client
                    </button>
                  </div>
                )} */}
              </div>
            </CardContent>
          </Card>

          {/* Email Capture */}
          <Card className="mb-10 healthcare-card hover:border-2 hover:border-teal-500">
            <CardHeader className="text-center font-inter font-light">
              <CardTitle className="font-inter font-light">Get Your AI-Powered Answer</CardTitle>
              <CardDescription>
                Almost there — where should we send your personalized response?
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleEmailSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="flex-1"
                  required
                />
                <Button className=" bg-[#14b8a6] text-white cursor-pointer" type="submit" disabled={isSubmitted}>
                  {isSubmitted ? (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2 text-white" />
                      Sent!
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2 bg-[#14b8a6] text-white" />
                      Get Answer
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Waitlist CTA */}
          <Card className="mb-10 healthcare-card hover:border-2 hover:border-teal-500">
            <CardContent className="text-center py-12 space-y-6">
              <div className="space-y-4">
                <h3 className="text-2xl font-inter font-light">
                  Join Our Early Access Program
                </h3>
                <p className="text-sm text-muted-foreground font-inter font-light max-w-2xl mx-auto">
                  Be among the first 5,000 families to experience the future of pediatric care. 
                  Get 3 months premium access completely free.
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="text-md px-8 btn-healthcare bg-[#cbfbf1] font-inter font-medium text-black">
                  <Link href="/contact">
                    Join Early Access
                  </Link>
                </Button>
                {/* <Button variant="outline" size="lg" asChild className="btn-healthcare bg-[#14b8a6] text-white">
                  <Link href="/demo">
                    Schedule Demo
                  </Link>
                </Button> */}
              </div>
              
              <div className="pt-4">
                <Badge variant="destructive" className="text-sm bg-gradient-to-r from-teal-500 to-blue-400 font-inter font-medium">
                  First 5,000 get 3 months premium free
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
