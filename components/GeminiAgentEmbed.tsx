// GeminiAgentEmbed.tsx – drop‑in React component to embed your Cloud‑Run Gemini agent
// ------------------------------------------------------------
// Renders the Cloud‑Run page in a sandboxed iframe but styled so
// it looks native (no visible frame, fills its parent, takes your
// font + colors via CSS variables).
// ------------------------------------------------------------

import React, { HTMLAttributes } from "react";

/**
 * Props:
 *   url   – full https://… link to the Cloud‑Run app (default = latest revision)
 *   height – css height (e.g. "480px" or "100%", default 600px)
 *   className – extra tailwind / css classes for the wrapper div
 *   style – additional inline styles
 */
export interface GeminiAgentEmbedProps extends HTMLAttributes<HTMLDivElement> {
  url?: string;
  height?: string | number;
}

export const GeminiAgentEmbed: React.FC<GeminiAgentEmbedProps> = ({
  url = "https://gemini-audio-agent-vpwpfyjhra-uw.a.run.app", // latestReadyRevision URL from Cloud Run status
  height = 600,
  className = "",
  style,
  ...rest
}) => {
  const resolvedHeight = typeof height === "number" ? `${height}px` : height;

  return (
    <div
      className={`relative w-full overflow-hidden rounded-xl bg-background ${className}`}
      style={{ height: resolvedHeight, ...style }}
      {...rest}
    >
      <iframe
        src={url}
        title="Gemini Audio Agent"
        className="absolute inset-0 h-full w-full border-0"
        // keeps the iframe functional but isolates storage/cookies
        sandbox="allow-scripts allow-same-origin"
      />
    </div>
  );
};

export default GeminiAgentEmbed;

/*
Usage in a Next.js / React page:

import { GeminiAgentEmbed } from "@/components/GeminiAgentEmbed";

export default function Demo() {
  return (
    <section className="max-w-4xl mx-auto py-10">
      <h1 className="text-2xl font-semibold mb-4">Chat with Gemini (Audio)</h1>
      <GeminiAgentEmbed height="500px" className="shadow-lg" />
    </section>
  );
}

The wrapper div:
  • removes the iframe border,
  • inherits your page’s border‑radius / shadow via Tailwind classes,
  • overflows hidden to eliminate any internal scrollbars.

If you want the embedded page to inherit your fonts and CSS variables,
add this once globally (e.g. in globals.css):

iframe[src*="gemini-audio-agent"] {
  font-family: inherit;
  color-scheme: inherit;
}
*/
