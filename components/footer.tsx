"use client"

import * as React from "react"
import { Heart, Mail, Phone, MapPin, Linkedin, Twitter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import Image from "next/image"

const footerLinks = {
  product: [
    { name: "AI Assistant" },
    { name: "Health Tracking" },
    { name: "Care Journal" },
    { name: "Pricing" },
  ],
  company: [

    { name: "Contact" },
  ],
  resources: [
    { name: "Help Center" },
    { name: "API Docs" },
    { name: "Blog" },
    { name: "Community" },
  ],
  legal: [
    { name: "Privacy Policy" },
    { name: "Terms of Service" },
    { name: "HIPAA Compliance" },
    { name: "Security" },
  ],
}

const socialLinks = [
  { name: "LinkedIn", icon: Linkedin },
  { name: "Twitter", icon: Twitter },
]

export default function Footer() {
  const [email, setEmail] = React.useState("")

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Newsletter signup:", email)
    setEmail("")
  }

  return (
    <footer className="border-t border-teal-500 gradient-to-r from-teal-500 to-blue-600">
      <div className="container mx-auto px-4 py-16 ">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2 space-y-4">
            <div className="flex items-center space-x-2 font-inter font-light">
              <div className="flex h-16 w-16 items-center justify-center rounded-lg ">
                <Image src="/OKdarkTsp.png" alt="Our Kidz" width={200} height={200} />
              </div>
              <span className="font-space-grotesk text-sm font-light">our kidz</span>
            </div>
            <p className="text-muted-foreground font-inter font-light max-w-sm text-sm">
              AI-powered pediatric care platform empowering parents with trusted guidance
              and secure health tracking for their children.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm font-inter font-light text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2 text-sm font-inter font-light text-muted-foreground">
                <Phone className="h-4 w-4" />
                <span>1-800-OUR-KIDZ</span>
              </div>
              <div className="flex items-center space-x-2 text-sm font-inter font-light text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>Boise, ID</span>
              </div>
            </div>
          </div>

          {/* Product Links */}
          <div className="space-y-4">
            <h3 className="font-semibold">Product</h3>
            <ul className="space-y-2">
              {footerLinks.product.map((link) => (
                <li key={link.name}>
                  <span className="text-sm text-muted-foreground">
                    {link.name}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Links */}
          <div className="space-y-4">
            <h3 className="font-semibold">Company</h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <span className="text-sm text-muted-foreground">
                    {link.name}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources Links */}
          {/* <div className="space-y-4">
            <h3 className="font-semibold">Resources</h3>
            <ul className="space-y-2">
              {footerLinks.resources.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div> */}

          {/* Newsletter */}
          <div className="space-y-4">
            <h3 className="font-semibold">Stay Updated</h3>
            <p className="text-sm text-muted-foreground">
              Get the latest updates on pediatric AI and health insights.
            </p>
            <form onSubmit={handleNewsletterSubmit} className="space-y-2">
              <Input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="text-sm"
                required
              />
              <Button type="submit" size="sm" className="w-full">
                Subscribe
              </Button>
            </form>
          </div>
        </div>

        <Separator className="my-8  bg-gradient-to-r from-teal-500 to-blue-400 " />

        {/* Bottom Section */}
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 ">
          <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-4 text-sm text-muted-foreground">
            <span>© 2025 our kidz. All rights reserved.</span>
            <span className="hidden md:inline">•</span>
            <span>Educational use only; not medical advice.</span>
          </div>

          <div className="flex items-center space-x-4">
            {/* Legal Links */}
            <div className="flex items-center space-x-4 text-sm">
              {footerLinks.legal.map((link, index) => (
                <React.Fragment key={link.name}>
                  <span className="text-muted-foreground">
                    {link.name}
                  </span>
                  {index < footerLinks.legal.length - 1 && (
                    <span className="text-muted-foreground">•</span>
                  )}
                </React.Fragment>
              ))}
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-2">
              {socialLinks.map((social) => (
                <Button
                  key={social.name}
                  variant="ghost"
                  size="icon"
                  aria-label={social.name}
                  disabled
                >
                  <social.icon className="h-4 w-4" />
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
