# Gemini Live API Email Function Tool

This implementation provides a comprehensive email functionality for the Gemini Live API that captures user questions and AI responses, then emails them to specified recipients.

## Overview

The email function tool allows <PERSON> (the AI assistant) to automatically send conversation summaries to users via email when they provide their email address during the conversation.

## Components

### 1. Email API Route (`/app/api/send-conversation/route.ts`)

**Endpoint**: `POST /api/send-conversation`

**Features**:
- SMTP email sending via Hostinger
- Rate limiting (5 emails per minute per IP)
- Email validation
- Professional HTML email formatting
- Error handling and logging
- Security measures

**Request Body**:
```typescript
{
  recipient_email: string     // Required: Valid email address
  user_question: string       // Required: User's original question
  ai_response: string         // Required: AI's complete response
  subject?: string           // Optional: Custom email subject
  include_context?: boolean  // Optional: Include additional context
  conversation_context?: string // Optional: Extra conversation details
}
```

**Response**:
```typescript
{
  success: boolean
  message?: string
  messageId?: string
  timestamp?: string
  error?: string
}
```

### 2. Function Tool Definition (`/lib/email-function-tool.ts`)

**Tool Name**: `send_conversation_email`

**Description**: Captures and emails conversation summaries with professional formatting.

**Parameters**:
- `recipient_email` (required): Email destination
- `user_question` (required): Original user question
- `ai_response` (required): Complete AI response
- `subject` (optional): Custom email subject line
- `include_context` (optional): Include additional context
- `conversation_context` (optional): Extra conversation details

### 3. Gemini Live Integration (`/hooks/use-gemini-live.ts`)

**Integration Points**:
- Function tool registration in session config
- Tool call handling in message callbacks
- Response handling and status updates
- Error handling and user feedback

**System Instruction Updates**:
- Instructions for Mae to use the email function
- Guidance on when to call the function
- User confirmation patterns

## Setup Instructions

### 1. Environment Variables

Copy `.env.example` to `.env.local` and configure:

```bash
# SMTP Email Configuration
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASS=Three110409!!*
FROM_EMAIL=<EMAIL>
FROM_NAME=Our Kidz
```

### 2. Dependencies

The implementation uses:
- `nodemailer` for email sending
- `@types/nodemailer` for TypeScript support

Install with:
```bash
npm install nodemailer @types/nodemailer
```

### 3. SMTP Configuration

**Hostinger SMTP Settings**:
- Host: `smtp.hostinger.com`
- Port: `465` (SSL)
- Security: SSL/TLS
- Authentication: Username/Password

## Usage Flow

1. **User Interaction**: User asks Mae a question via voice
2. **AI Response**: Mae provides a helpful response
3. **Email Collection**: User provides their email address
4. **Function Call**: Mae calls `send_conversation_email` function
5. **Email Processing**: API validates, formats, and sends email
6. **Confirmation**: Mae confirms email was sent successfully

## Security Features

### Rate Limiting
- 5 emails per minute per IP address
- Prevents spam and abuse
- Automatic cleanup of expired rate limit data

### Email Validation
- Regex pattern validation for email format
- Prevents invalid email submissions

### Input Sanitization
- Content sanitization for email body
- Protection against injection attacks

### Environment Security
- SMTP credentials stored in environment variables
- No hardcoded credentials in source code

### Error Handling
- Comprehensive error logging
- User-friendly error messages
- Graceful degradation

## Email Template

The email includes:
- **Professional Our Kidz branding**
- **Conversation timestamp**
- **User's original question** (highlighted section)
- **Mae's complete response** (highlighted section)
- **Optional context** (if included)
- **Footer with platform information**

**HTML Styling**:
- Responsive design
- Professional color scheme (#00bba7 teal theme)
- Clear section separation
- Mobile-friendly layout

## Testing

### Manual Testing
```typescript
import { testEmailConversation } from '../lib/test-email'

// Test the email functionality
await testEmailConversation('<EMAIL>')
```

### Validation
```typescript
import { validateEmailSetup } from '../lib/test-email'

// Validate environment configuration
validateEmailSetup()
```

## Error Scenarios

### Common Errors
1. **Invalid Email**: Returns 400 with validation error
2. **Rate Limit**: Returns 429 when limit exceeded
3. **SMTP Error**: Returns 500 with email service error
4. **Missing Env**: Returns 500 when SMTP not configured

### Error Messages
- User-friendly messages for frontend display
- Detailed logging for debugging
- Proper HTTP status codes

## Function Call Example

When Mae receives an email address, she automatically calls:

```javascript
{
  name: "send_conversation_email",
  args: {
    recipient_email: "<EMAIL>",
    user_question: "How can I help my child sleep better?",
    ai_response: "Here are some strategies for better sleep...",
    subject: "Sleep Solutions for Your Child - Our Kidz Consultation",
    include_context: true,
    conversation_context: "Initial consultation about sleep habits"
  }
}
```

## Monitoring

### Logging
- All email attempts logged with timestamps
- Error details logged for debugging
- Rate limit violations tracked

### Status Updates
- Real-time status updates in UI
- Success/failure notifications
- Processing indicators

## Future Enhancements

1. **Database Storage**: Store conversation history
2. **Advanced Rate Limiting**: Redis-based rate limiting
3. **Email Templates**: Multiple template options
4. **Analytics**: Email delivery tracking
5. **Attachment Support**: PDF conversation exports
6. **User Preferences**: Email format preferences