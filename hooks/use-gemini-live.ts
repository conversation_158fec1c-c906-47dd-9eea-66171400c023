//--------------------------------------------------------------
// hooks/use‑gemini‑live.ts
//--------------------------------------------------------------

import { useState, useRef, useCallback } from 'react';
import {
  GoogleGenAI,
  LiveServerMessage,
  Modality,
  Session
} from '@google/genai';
import { decode, decodeAudioData, createBlob } from '../utils';
import { emailConversationTool, handleEmailConversation } from '../lib/email-function-tool';

//--------------------------------------------------------------
// Constant: the system instruction text
//--------------------------------------------------------------
const SYSTEM_INSTRUCTION_TEXT = `
### Personality and Tone

#### Identity

Mae is a warm, friendly digital pediatric health coach with clinical experience as a nurse. She's transitioned into a virtual guide for families, offering relatable health insights with a personal touch. Mae is the reassuring voice in the room, ready with gentle humor and compassionate support.

#### Other details

* Adjust explanations for the child's age or developmental stage.
* Use plain English for any clinical terms and define them clearly if used.
* Always include safety reminders when giving health guidance, without sounding alarming.
* Never use XML or any tags when speaking to users.

### Instructions

* Follow the Conversation States precisely for structured interactions.
* Never ask users to format questions with tags like [user_question]. Just ask them to speak normally.
* If a user shares personal details (email, phone, child's name), **confirm back slowly and clearly**, spelling it out when needed.
* If corrected, acknowledge and confirm the new version.
* Stay warm and conversational. Be empathetic even when collecting factual details.
* Handle one topic at a time. Wait for the user's input before moving forward.
* Use the [search_web] and [send_conversation_email] tools as needed.

---

### Conversation States

[
  {
    "id": "1_greeting",
    "description": "Greet the user and introduce Mae and the Our Kids platform.",
    "instructions": [
      "Deliver a warm, friendly welcome.",
      "Introduce yourself as Mae, the AI pediatric health guide.",
      "Explain that you're here to answer questions or provide help regarding child health and the Our Kids platform."
    ],
    "examples": [
      "Hi there! I'm Mae—your digital pediatric health coach with Our Kids. I'm here to help you feel confident and informed when it comes to your childs well-being. Whats on your mind today?",
      "Hello! I'm Mae, your guide for kids' health and parenting questions. Ask me anything—I'm happy to help!"
    ],
    "transitions": [
      {
        "next_step": "2_receive_question",
        "condition": "After the greeting is complete."
      }
    ]
  },
  {
    "id": "2_receive_question",
    "description": "Prompt the user for their question and confirm your understanding.",
    "instructions": [
      "Invite the user to ask a health or parenting question in their own words.",
      "Restate or paraphrase the question in plain language to confirm your understanding."
    ],
    "examples": [
      "You can go ahead and ask your question—anything related to your child's health, behavior, or development.",
      "So just to make sure, you're asking how to help your toddler with teething pain—did I get that right?"
    ],
    "transitions": [
      {
        "next_step": "3_clarify_question",
        "condition": "If the question is unclear or missing context (like child's age or symptoms)."
      },
      {
        "next_step": "4_provide_response",
        "condition": "If the question is clear and can be answered right away."
      }
    ]
  },
  {
    "id": "3_clarify_question",
    "description": "Ask for additional context or missing details.",
    "instructions": [
      "Gently point out what info you need to help better (e.g., age, symptom duration).",
      "Be specific and empathetic in your clarification."
    ],
    "examples": [
      "To give the best tips, could you tell me how old your child is?",
      "Got it—are we talking about a daytime cough or more at night?"
    ],
    "transitions": [
      {
        "next_step": "2_receive_question",
        "condition": "Once the user supplies the missing info."
      }
    ]
  },
  {
    "id": "4_provide_response",
    "description": "Provide a helpful answer and strongly encourage sharing an email for a follow-up summary, also offering search.",
    "instructions": [
      "Offer a clear, concise response tailored to the child's age and situation.",
      "Clearly and warmly state that you can send a detailed summary of the conversation and relevant resources to their email, emphasizing it's a great way to keep the information handy.",
      "Prompt for the user's email address, explaining its benefit.",
      "If the answer requires current information (e.g., recalls, latest safety tips), use the [search_web] function to assist."
    ],
    "examples": [
      "You can soothe teething by gently massaging the gums or offering a cool silicone teether. To make sure you have all these tips handy, I'd love to send you a more detailed guide and some extra resources straight to your email. What's the best email address for me to send it to?",
      "That sounds like seasonal allergies. A saline rinse might help, and limiting outdoor play during high pollen times. I can send you a summary of these tips and more information about managing allergies directly to your inbox so you don't have to remember it all. Would you like me to email you more tips? If so, what's your email address?"
    ],
    "thoughts": [
      "The user might be hesitant to share their email, so I need to clearly explain the benefit of receiving a summary.",
      "Ensure I transition smoothly to the email request after providing the initial answer."
    ],
    "transitions": [
      {
        "next_step": "5_handle_email",
        "condition": "After offering to send the email summary and receiving an email address."
      },
      {
        "next_step": "6_conclusion",
        "condition": "If the user declines the email and no further assistance is needed."
      }
    ]
  },
  {
    "id": "5_handle_email",
    "description": "Confirm and send the summary to the user via email, ensuring no verbalization of internal commands.",
    "instructions": [
      "Repeat the email address back clearly, spelling it out, but without using dashes between letters (e.g., 'J E N N Y' not 'J-E-N-N-Y').",
      "Without verbalizing any tool names or internal commands, use the email tool to send the data.",
      "Confirm the email was sent and offer any final tips or support."
    ],
    "examples": [
      "Just to confirm—<NAME_EMAIL>? That's J E N N Y dot S M I T H at Gmail dot com?",
      "Great, I'm sending your <NAME_EMAIL> now. It should land in your inbox shortly!",
      "I've just sent that over to you. It should be in your inbox soon!"
    ],
    "thoughts": [
      "Need to be precise in confirming the email address, spelling it out clearly and slowly.",
      "The confirmation message should be friendly and reassuring.",
      "Make sure to activate the email sending tool with the correct parameters without mentioning the tool itself."
    ],
    "transitions": [
      {
        "next_step": "6_conclusion",
        "condition": "Once the email is confirmed and sent."
      }
    ]
  },
  {
    "id": "6_conclusion",
    "description": "Wrap up the session with encouragement and warmth.",
    "instructions": [
      "Thank the user and offer a positive statement about their role as a caregiver.",
      "Let them know they can return anytime for support."
    ],
    "examples": [
      "Thanks for your question! Every step you take builds your child's healthy future. You're doing great!",
      "Glad I could help—keep asking, keep growing. You've got this!"
    ],
    "thoughts": [
      "End the conversation on a high, supportive note.",
      "Reiterate the platform's availability for future help."
    ],
    "transitions": []
  }
]  
`;

//--------------------------------------------------------------
// AudioWorklet processor source (runs in a separate thread)
//--------------------------------------------------------------
const audioProcessor = `
 class AudioProcessor extends AudioWorkletProcessor {
   process(inputs) {
     const input = inputs[0];
     if (input?.length && input[0]?.length) this.port.postMessage(input[0]);
     return true;
   }
 }
 registerProcessor('audio-processor', AudioProcessor);
`;

//--------------------------------------------------------------
// React hook
//--------------------------------------------------------------
export function useGeminiLive() {
  // ---------- state ----------
  const [status,          setStatus]          = useState('Not initialized');
  const [error,           setError]           = useState('');
  const [isRecording,     setIsRecording]     = useState(false);
  const [isModelSpeaking, setIsModelSpeaking] = useState(false);
  const [isConnected,     setIsConnected]     = useState(false);

  // ---------- refs ----------
  const clientRef            = useRef<GoogleGenAI>();
  const sessionRef           = useRef<Session>();

  const inputAudioContextRef  = useRef<AudioContext>();
  const outputAudioContextRef = useRef<AudioContext>();
  const mediaStreamRef        = useRef<MediaStream>();
  const audioWorkletNodeRef   = useRef<AudioWorkletNode>();

  const sourcesRef            = useRef(new Set<AudioBufferSourceNode>());
  const nextStartTimeRef      = useRef(0);

  // Has the empty user turn already been sent for this live session?
  const greetingSentRef       = useRef(false);

  // ---------- helpers ----------
  const updateStatus = useCallback(
    (msg: string) => { console.log('[GeminiLive]', msg); setStatus(msg); },
    []
  );
  const updateError = useCallback(
    (msg: string) => { console.error('[GeminiLive]', msg); setError(msg); },
    []
  );

  //------------------------------------------------------------
  // Send initial user turn to trigger the greeting with custom prompt
  //------------------------------------------------------------
  function sendInitialUserTurn(customPrompt?: string) {
    if (greetingSentRef.current) return;   // only once per session
    greetingSentRef.current = true;

    const initialMessage = customPrompt || '';
    
    sessionRef.current?.sendClientContent({
      turns: [{ role: 'user', parts: [{ text: initialMessage }] }],
      turnComplete: true,
    });
  }

  //------------------------------------------------------------
  // 1. Session + client initialisation
  //------------------------------------------------------------
  const initSession = useCallback(async () => {
    if (!clientRef.current) return updateError('Client not initialized.');
    if (sessionRef.current) return updateStatus('Session already exists.');

    updateStatus('Initializing session…');

    try {
      sessionRef.current = await clientRef.current.live.connect({
        model: 'gemini-2.0-flash-live-001', //gemini-2.5-flash-preview-native-audio-dialog
        config: {
          responseModalities: [Modality.AUDIO],
          tools: [
            { googleSearch: {} },
            { functionDeclarations: [emailConversationTool] }
          ],
          speechConfig: {
            languageCode: 'en-US',
            voiceConfig : { prebuiltVoiceConfig: { voiceName: 'Zephyr' } }
          },
          // System instruction stays here — this is where Gemini expects it.
          systemInstruction: { parts: [{ text: SYSTEM_INSTRUCTION_TEXT }] }
        },
        callbacks: {
          onopen: () => {
            setIsConnected(true);
            updateStatus('🔗 Connected – you can talk now.');
            // Don't send initial message here - we'll do it after microphone setup
          },
          onclose: (e: CloseEvent) => {
            setIsConnected(false);
            updateStatus(`Connection closed: ${e.reason || 'normal'}`);
            sessionRef.current = undefined;
            greetingSentRef.current = false;  // reset for next session
          },
          onerror: (e: ErrorEvent) => {
            setIsConnected(false);
            updateError(`Session error: ${e.message}`);
          },

          // -------- audio from Gemini --------
          onmessage: async (msg: LiveServerMessage) => {
            const firstPart  = msg.serverContent?.modelTurn?.parts?.[0] as any;
            const inline     = firstPart?.inlineData;
            const speechFlag = ('speechState' in (firstPart ?? {}))
              ? firstPart.speechState === 'SPEECH_START'
              : false;
            setIsModelSpeaking(speechFlag);

            if (msg.serverContent?.interrupted) {
              updateStatus('Speech interrupted.');
              sourcesRef.current.forEach(src => src.stop());
              sourcesRef.current.clear();
              nextStartTimeRef.current = 0;
              return;
            }

            if (inline?.data) {
              const outCtx = outputAudioContextRef.current!;
              const gain   = outCtx.createGain();
              gain.connect(outCtx.destination);

              nextStartTimeRef.current =
                Math.max(nextStartTimeRef.current, outCtx.currentTime);

              const buffer = await decodeAudioData(
                decode(inline.data), outCtx, 24_000, 1
              );
              const src = outCtx.createBufferSource();
              src.buffer = buffer;
              src.connect(gain);
              src.onended = () => {
                gain.disconnect();
                sourcesRef.current.delete(src);
              };

              src.start(nextStartTimeRef.current);
              nextStartTimeRef.current += buffer.duration;
              sourcesRef.current.add(src);
            }

            // Handle tool calls
            if (msg.toolCall?.functionCalls) {
              console.log('🔧 Received tool call:', msg.toolCall);
              updateStatus('Processing email request...');
              
              const functionCalls = msg.toolCall.functionCalls;
              const functionResponses = [];

              for (const call of functionCalls) {
                if (call.name === 'send_conversation_email') {
                  try {
                    if (!call.args) {
                      console.error('❌ Tool call missing arguments:', call);
                      updateError(`Tool call 'send_conversation_email' missing arguments.`);
                      continue;
                    }
                    console.log('📧 Executing email function with args:', call.args);
                    const result = await handleEmailConversation(call.args as any);
                    console.log('📧 Email function result:', result);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                    
                    if (result.success) {
                      updateStatus('Email sent successfully!');
                    } else {
                      updateError(`Email failed: ${result.error}`);
                    }
                  } catch (error) {
                    console.error('Error executing email function:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                    updateError('Failed to send email');
                  }
                }
              }

              // Send function responses back to Gemini
              if (functionResponses.length > 0) {
                console.log('📤 Sending function responses to Gemini:', functionResponses);
                sessionRef.current?.sendToolResponse({
                  functionResponses: functionResponses
                });
                console.log('✅ Function responses sent');
              }
            }
          }
        }
      });
      updateStatus('✅ Session ready.');
    } catch (e) {
      updateError(`Failed to init session: ${
        e instanceof Error ? e.message : String(e)
      }`);
    }
  }, [updateStatus, updateError]);

  const initClient = useCallback(async () => {
    updateStatus('Creating client…');
    try {
      const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;
      if (!apiKey) {
        updateError('Gemini API key is missing. Please check your environment variables.');
        return;
      }

      if (!clientRef.current) {
        clientRef.current = new GoogleGenAI({ apiKey });
      }

      if (typeof window === 'undefined') {
        updateError('Audio functionality requires a browser environment.');
        return;
      }

      const AudioCtor =
        window.AudioContext || (window as any).webkitAudioContext;

      if (!AudioCtor) {
        updateError('Web Audio API is not supported in this browser.');
        return;
      }

      if (!outputAudioContextRef.current) {
        outputAudioContextRef.current = new AudioCtor({ sampleRate: 24_000 });
      }
      if (!inputAudioContextRef.current) {
        inputAudioContextRef.current  = new AudioCtor({ sampleRate: 16_000 });
      }
      await initSession();
    } catch (e) {
      updateError(`Client init failed: ${
        e instanceof Error ? e.message : String(e)
      }`);
    }
  }, [initSession, updateStatus, updateError]);

  //------------------------------------------------------------
  // 2. Recording helpers
  //------------------------------------------------------------
  const stopRecording = useCallback(() => {
    if (!isRecording) return;

    updateStatus('Stopping recording…');
    setIsRecording(false);

    audioWorkletNodeRef.current?.port.postMessage('stop');
    audioWorkletNodeRef.current?.disconnect();
    audioWorkletNodeRef.current = undefined;

    mediaStreamRef.current?.getTracks().forEach(t => t.stop());
    mediaStreamRef.current = undefined;

    updateStatus('Recording stopped.');
  }, [isRecording, updateStatus]);

  const startRecording = useCallback(async (initialMessage?: string) => {
    if (isRecording) return updateStatus('Already recording.');
    
    // Initialize client if not ready
    if (!clientRef.current) {
      await initClient();
    }
    
    // Create new session if needed
    if (!sessionRef.current) {
      await initSession();
    }
    
    if (!sessionRef.current) return updateError('Session not ready.');

    await inputAudioContextRef.current?.resume();
    await outputAudioContextRef.current?.resume();
    updateStatus('Requesting microphone access…');

    try {
      if (!navigator.mediaDevices?.getUserMedia) {
        updateError('Microphone access is not supported in this browser.');
        return;
      }

      mediaStreamRef.current = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16_000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
      updateStatus('Mic access granted.');

      const ac = inputAudioContextRef.current!;
      if (!ac.audioWorklet) {
        updateError('AudioWorklet is not supported in this browser.');
        return;
      }

      const blob = new Blob([audioProcessor], { type: 'application/javascript' });
      const moduleURL = URL.createObjectURL(blob);

      try {
        await ac.audioWorklet.addModule(moduleURL);
        updateStatus('AudioWorklet loaded.');
      } catch (workletError) {
        updateError(`Failed to load AudioWorklet: ${
          workletError instanceof Error ? workletError.message : String(workletError)
        }`);
        return;
      } finally {
        URL.revokeObjectURL(moduleURL);
      }

      const srcNode = ac.createMediaStreamSource(mediaStreamRef.current);
      const wkNode  = new AudioWorkletNode(ac, 'audio-processor', {
        numberOfInputs: 1,
        numberOfOutputs: 1,
        outputChannelCount: [1]
      });
      audioWorkletNodeRef.current = wkNode;

      wkNode.port.onmessage = (evt) => {
        try {
          const pcm = evt.data as Float32Array;
          if (!pcm?.some(s => s !== 0)) return;    // skip silence
          sessionRef.current?.sendRealtimeInput({ audio: createBlob(pcm) });
        } catch (err) {
          updateError(`Streaming error: ${
            err instanceof Error ? err.message : String(err)
          }`);
        }
      };

      srcNode.connect(wkNode);      // no echo → don't connect to destination
      setIsRecording(true);
      updateStatus('🔴 Recording… Mae is responding.');
      
      // Send initial message to trigger immediate AI response
      if (initialMessage) {
        sendInitialUserTurn(initialMessage);
      } else {
        sendInitialUserTurn();
      }
    } catch (err) {
      updateError(`Start recording failed: ${
        err instanceof Error ? err.message : String(err)
      }`);
      stopRecording();
    }
  }, [isRecording, stopRecording, updateStatus, updateError]);

  //------------------------------------------------------------
  // 3.  public API
  //------------------------------------------------------------
  return {
    status,
    error,
    isRecording,
    isModelSpeaking,
    isConnected,
    initClient,     // call once on‑mount
    startRecording,
    stopRecording
  };
}
