@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 174 100% 37%;
    --primary-foreground: 0 0% 98%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 174 100% 37%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 174 100% 37%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 174 100% 37%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1.2;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer components {
  .healthcare-card {
    /* Modern layered drop shadow with teal glow for depth */
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.1),
      0 1px 2px 0 rgba(0, 0, 0, 0.06),
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(20, 184, 166, 0.05),
      0 0 20px rgba(20, 184, 166, 0.08);

    /* Smooth transition for all shadow and transform effects */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Subtle transform for better hover feedback */
    transform: translateY(0);
  }

  .healthcare-card:hover {
    /* Enhanced shadow with stronger teal glow on hover for premium feel */
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(20, 184, 166, 0.15),
      0 0 30px rgba(20, 184, 166, 0.2),
      0 0 60px rgba(20, 184, 166, 0.1);

    /* Subtle lift effect */
    transform: translateY(-4px);
  }

  /* Enhanced shadow with teal glow for dark mode */
  .dark .healthcare-card {
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.2),
      0 1px 2px 0 rgba(0, 0, 0, 0.12),
      0 4px 6px -1px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(20, 184, 166, 0.1),
      0 0 25px rgba(20, 184, 166, 0.15);
  }

  .dark .healthcare-card:hover {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.2),
      0 2px 4px -1px rgba(0, 0, 0, 0.12),
      0 10px 15px -3px rgba(0, 0, 0, 0.2),
      0 20px 25px -5px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(20, 184, 166, 0.2),
      0 0 40px rgba(20, 184, 166, 0.25),
      0 0 80px rgba(20, 184, 166, 0.15);
  }
}
