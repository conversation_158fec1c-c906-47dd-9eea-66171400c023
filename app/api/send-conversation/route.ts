import { NextRequest, NextResponse } from 'next/server'
const nodemailer = require('nodemailer')

// Rate limiting store (in production, use Redis or a database)
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 5 // 5 emails per minute per IP

interface ConversationData {
  recipient_email: string
  subject?: string
  user_question: string
  ai_response: string
  timestamp: string
  session_id?: string
  include_context?: boolean
  conversation_context?: string
}

function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  // Reset count if window has passed
  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  // Check if within rate limit
  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  // Increment count
  clientData.count++
  clientData.lastRequest = now
  return true
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || //check this if we see any errors
      request.headers.get('x-forwarded-for')?.split(',')[0] || 
      request.headers.get('x-real-ip') || 
      'unknown'

    // Check rate limit
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please wait before sending another email.' },
        { status: 429 }
      )
    }

    const body: ConversationData = await request.json()

    // Validate required fields
    if (!body.recipient_email || !body.user_question || !body.ai_response) {
      return NextResponse.json(
        { error: 'Missing required fields: recipient_email, user_question, ai_response' },
        { status: 400 }
      )
    }

    // Validate email format
    if (!validateEmail(body.recipient_email)) {
      return NextResponse.json(
        { error: 'Invalid email address format' },
        { status: 400 }
      )
    }

    // Get SMTP credentials from environment variables (use exact values provided)
    const smtpHost = process.env.SMTP_HOST || 'smtp.hostinger.com'
    const smtpPort = parseInt(process.env.SMTP_PORT || '465')
    const smtpUser = process.env.SMTP_USER || '<EMAIL>'
    const smtpPass = process.env.SMTP_PASS || 'Three110409!!*'
    const fromEmail = process.env.FROM_EMAIL || '<EMAIL>'
    const fromName = process.env.FROM_NAME || 'Our Kidz'

    console.log('📧 SMTP Configuration:', {
      host: smtpHost,
      port: smtpPort,
      user: smtpUser,
      hasPassword: !!smtpPass,
      fromEmail,
      fromName
    })

    if (!smtpUser || !smtpPass) {
      console.error('❌ SMTP credentials not configured')
      return NextResponse.json(
        { error: 'Email service not configured' },
        { status: 500 }
      )
    }

    // Create transporter
    console.log('🔧 Creating SMTP transporter...')
    console.log('📦 Nodemailer type:', typeof nodemailer)
    console.log('📦 Nodemailer methods:', Object.keys(nodemailer))
    
    const transporter = nodemailer.createTransport({
      host: smtpHost,
      port: smtpPort,
      secure: true, // true for 465, false for other ports
      auth: {
        user: smtpUser,
        pass: smtpPass,
      },
      debug: true, // Enable debug logging
      logger: true // Enable logger
    })

    // Test the connection
    try {
      console.log('🔌 Testing SMTP connection...')
      await transporter.verify()
      console.log('✅ SMTP connection verified successfully')
    } catch (verifyError) {
      console.error('❌ SMTP connection verification failed:', verifyError)
      return NextResponse.json(
        { 
          error: 'SMTP connection failed',
          details: verifyError instanceof Error ? verifyError.message : 'Unknown SMTP error'
        },
        { status: 500 }
      )
    }

    // Format email content
    const subject = body.subject || 'Your Our Kidz AI Conversation Summary'
    const formattedTimestamp = new Date(body.timestamp).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    })

    let emailHTML = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Kidz Conversation Summary</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .container {
            background-color: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #00bba7;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            color: #00bba7;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .timestamp {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .conversation-section {
            margin-bottom: 25px;
            padding: 20px;
            border-radius: 8px;
        }
        .user-section {
            background-color: #f0f9ff;
            border-left: 4px solid #0ea5e9;
        }
        .ai-section {
            background-color: #f0fdf4;
            border-left: 4px solid #00bba7;
        }
        .section-title {
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .content {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #666;
            font-size: 14px;
        }
        .footer a {
            color: #00bba7;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">Our Kidz</div>
            <div>AI-Powered Parenting Platform</div>
        </div>
        
        <div class="timestamp">
            <strong>Conversation Date:</strong> ${formattedTimestamp}
        </div>
        
        <div class="conversation-section user-section">
            <div class="section-title">Your Question:</div>
            <div class="content">${body.user_question}</div>
        </div>
        
        <div class="conversation-section ai-section">
            <div class="section-title">Mae's Response:</div>
            <div class="content">${body.ai_response}</div>
        </div>`

    // Add conversation context if included
    if (body.include_context && body.conversation_context) {
      emailHTML += `
        <div class="conversation-section" style="background-color: #fefce8; border-left: 4px solid #eab308;">
            <div class="section-title">Additional Context:</div>
            <div class="content">${body.conversation_context}</div>
        </div>`
    }

    emailHTML += `
        <div class="footer">
            <p>This conversation summary was generated by Our Kidz AI platform.</p>
            <p>Visit us at <a href="https://our-kids.vercel.app">our-kids.vercel.app</a></p>
            <p>Your journey to health freedom starts here!</p>
        </div>
    </div>
</body>
</html>`

    // Text version for email clients that don't support HTML
    let emailText = `Our Kidz - AI Conversation Summary\n\n`
    emailText += `Conversation Date: ${formattedTimestamp}\n\n`
    emailText += `Your Question:\n${body.user_question}\n\n`
    emailText += `Mae's Response:\n${body.ai_response}\n\n`
    
    if (body.include_context && body.conversation_context) {
      emailText += `Additional Context:\n${body.conversation_context}\n\n`
    }
    
    emailText += `This conversation summary was generated by Our Kidz AI platform.\n`
    emailText += `Visit us at https://our-kids.vercel.app\n`
    emailText += `Your journey to health freedom starts here!`

    // Send email
    const mailOptions = {
      from: `"${fromName}" <${fromEmail}>`,
      to: body.recipient_email,
      subject: subject,
      text: emailText,
      html: emailHTML,
    }

    console.log('📮 Sending email with options:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject,
      textLength: emailText.length,
      htmlLength: emailHTML.length
    })

    const info = await transporter.sendMail(mailOptions)
    console.log('✅ Email sent successfully:', {
      messageId: info.messageId,
      response: info.response
    })

    return NextResponse.json({
      success: true,
      message: 'Conversation summary sent successfully',
      messageId: info.messageId,
      timestamp: body.timestamp
    })

  } catch (error) {
    console.error('❌ Error sending email:', error)
    
    // Log more detailed error information
    if (error instanceof Error) {
      console.error('❌ Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack?.substring(0, 500)
      })
    }
    
    return NextResponse.json(
      { 
        error: 'Failed to send email',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}