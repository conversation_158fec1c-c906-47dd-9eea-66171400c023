import { NextResponse } from 'next/server'

export async function GET() {
  const config = {
    SMTP_HOST: process.env.SMTP_HOST || 'NOT_SET',
    SMTP_PORT: process.env.SMTP_PORT || 'NOT_SET',
    SMTP_USER: process.env.SMTP_USER || 'NOT_SET',
    SMTP_PASS: process.env.SMTP_PASS ? 'SET' : 'NOT_SET',
    FROM_EMAIL: process.env.FROM_EMAIL || 'NOT_SET',
    FROM_NAME: process.env.FROM_NAME || 'NOT_SET'
  }

  return NextResponse.json(config)
}