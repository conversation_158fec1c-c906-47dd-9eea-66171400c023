import type React from "react"
import "./globals.css"
import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON>pins } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-poppins",
})

export const metadata: Metadata = {
  metadataBase: new URL("https://our-kids.vercel.app"),
  icons: {
    icon: "/OKdarkTsp.png",
  },
  title: "our kidz - AI-Powered Parenting Platform",
  description: "Revolutionizing parenting with AI-powered insights.",
    generator: 'v0.1.0'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={poppins.className}>
        <ThemeProvider defaultTheme="light" storageKey="our-kidz-theme">
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
