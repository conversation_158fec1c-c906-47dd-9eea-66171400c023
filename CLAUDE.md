# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server (Next.js)
- `npm run build` - Build production bundle  
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Environment Variables

- `NEXT_PUBLIC_GEMINI_API_KEY` - Required for Gemini Live API integration

## Architecture Overview

This is a Next.js 15 landing page for "Our Kidz", an AI-powered parenting platform with live voice interaction capabilities.

### Key Components

**GeminiAgentEmbed** (`components/GeminiAgentEmbed.tsx`)
- Embeds Cloud Run Gemini agent via iframe
- Default URL: `https://gemini-audio-agent-vpwpfyjhra-uw.a.run.app`
- Configurable height and styling

**useGeminiLive Hook** (`hooks/use-gemini-live.ts`)
- Manages WebSocket connection to Gemini Live API
- Handles real-time audio input/output with AudioWorklet
- System instruction configures "<PERSON>" as guide character
- Features automatic greeting on connection
- Audio processing: 16kHz input, 24kHz output

**Audio Utils** (`utils.ts`)
- Audio encoding/decoding utilities for Gemini Live API
- PCM audio format conversion (Float32 ↔ Int16)
- Base64 encoding for audio transmission

### UI Architecture

- **Styling**: Tailwind CSS with custom teal color scheme (`#00bba7`)
- **Theme**: Light theme with ThemeProvider
- **Font**: Poppins from Google Fonts
- **Components**: Shadcn/ui component library
- **Icons**: Lucide React

### Build Configuration

- ESLint and TypeScript errors ignored during builds (`next.config.mjs`)
- Images unoptimized for static export compatibility
- Tailwind configured for all JS/TS/JSX/TSX files

### Live API Integration

Uses Google's Gemini Live API v1beta with:
- Model: `gemini-2.0-flash-live-001`
- Voice: Zephyr (en-US)
- Response modality: Audio only
- Tools: Google Search enabled
- Real-time audio streaming via WebSocket

The system sends an empty user turn on connection to trigger Mae's greeting, and handles audio interruption/resumption smoothly.

### Email Function Tool

**Email Functionality** (`lib/email-function-tool.ts`, `app/api/send-conversation/route.ts`)
- Gemini Live API function tool for sending conversation summaries via email
- SMTP integration with Hostinger (smtp.hostinger.com:465)
- Professional HTML email templates with Our Kidz branding
- Rate limiting (5 emails/minute per IP) and security measures
- Function tool registered in Gemini Live session config

**Environment Variables Required**:
```bash
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASS=Three110409!!*
FROM_EMAIL=<EMAIL>
FROM_NAME=Our Kidz
```

**Debug Tools**:
- `/debug-email` page for testing email functionality manually
- Console logging with emojis for debugging Live API function calls
- Test utilities in `lib/test-email.ts` for validation