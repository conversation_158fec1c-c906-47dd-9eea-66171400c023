// ------------------------------------------------------------
//  Builds a FunctionDeclaration (Gemini SDK) from the JSON
//  schema you already have in `email-function-tool.ts`.
// ------------------------------------------------------------
import {
    FunctionDeclaration,
    Schema,
    Type,                     // Enum from @google/genai
  } from '@google/genai';
  import { emailConversationTool } from './email-function-tool';
  
  export const emailToolDeclaration: FunctionDeclaration = {
    name: emailConversationTool.name,
    description: emailConversationTool.description,
    parameters: emailConversationTool.parameters as unknown as Schema, // cast OK – shapes match
  };
  