// ---------------------------------------------------------------------------
//  email-function-tool.ts
//  --------------------------------------------------------------------------
//  Function‑tool definition + helper for Gemini Live “send_conversation_email”
//  – Adds resources[] + disclaimers
//  – Ensures user question, answer, resources, and disclaimers are forwarded
// ---------------------------------------------------------------------------

import { Type } from '@google/genai';

/* ------------------------------------------------------------------ *
 * 1. JSON‑schema that Gemini exposes to the LLM as a callable “tool” *
 * ------------------------------------------------------------------ */
export const emailConversationTool = {
  name: 'send_conversation_email',
  description:
    'Send the full conversation recap—including the caregiver’s original question, Mae’s answer, any cited resources, and disclaimers—to the specified e‑mail address in a clear, professional format.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      recipient_email: {
        type: Type.STRING,
        description:
          'Destination e‑mail address. Must be RFC‑5322 compliant.',
        pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'
      },
      user_question: {
        type: Type.STRING,
        description:
          "The caregiver's exact question, captured verbatim."
      },
      ai_response: {
        type: Type.STRING,
        description:
          "Mae's final, age‑appropriate guidance responding to the caregiver’s question."
      },
      resources: {
        type: Type.ARRAY,
        description:
          'Optional list of evidence‑based resources (title + url) that Mae referenced.',
        items: {
          type: Type.OBJECT,
          properties: {
            title: {
              type: Type.STRING,
              description: 'Human‑readable title of the resource.'
            },
            url: {
              type: Type.STRING,
              description: 'Direct HTTPS link to the resource.'
            }
          },
          required: ['title', 'url']
        }
      },
      subject: {
        type: Type.STRING,
        description:
          'Optional custom subject line for the e‑mail.',
        default: 'Your Our Kidz AI Conversation Summary'
      },
      disclaimers: {
        type: Type.STRING,
        description:
          'Plain‑language disclaimers appended to the e‑mail (e.g., “Educational only, not a diagnosis”).'
      },
      include_context: {
        type: Type.BOOLEAN,
        description:
          'Whether to include additional conversation context beyond the immediate Q&A.',
        default: false
      },
      conversation_context: {
        type: Type.STRING,
        description:
          'Optional additional context (system prompt, previous turns, etc.).'
      }
    },
    required: ['recipient_email', 'user_question', 'ai_response']
  }
} as const;

/* ------------------------------------------------------------------ *
 * 2. TypeScript helpers                                              *
 * ------------------------------------------------------------------ */
export interface EmailResource {
  title: string;
  url: string;
}

export interface EmailConversationParams {
  recipient_email: string;
  user_question: string;
  ai_response: string;
  resources?: EmailResource[];
  subject?: string;
  disclaimers?: string;
  include_context?: boolean;
  conversation_context?: string;
}

export interface EmailConversationResult {
  success: boolean;
  message?: string;
  error?: string;
  timestamp?: string;
  messageId?: string;
}

/* ------------------------------------------------------------------ *
 * 3. Helper that front‑end calls when the LLM issues the tool‑call    *
 * ------------------------------------------------------------------ */
export async function handleEmailConversation(
  parameters: EmailConversationParams
): Promise<EmailConversationResult> {
  try {
    // --- Basic validation -------------------------------------------------
    const { recipient_email, user_question, ai_response } = parameters;
    if (!recipient_email || !user_question || !ai_response) {
      return {
        success: false,
        error:
          'Missing required parameters: recipient_email, user_question, or ai_response'
      };
    }

    // --- Build payload ----------------------------------------------------
    const emailData = {
      ...parameters,
      timestamp: new Date().toISOString(),
      session_id: `session_${Date.now()}_${Math.random()
        .toString(36)
        .slice(2, 10)}`
    };

    console.log('📧 Sending email with data:', emailData);

    // --- Call back‑end /api/send-conversation -----------------------------
    const baseUrl =
      typeof window !== 'undefined' ? window.location.origin : '';
    const apiUrl = `${baseUrl}/api/send-conversation`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(emailData)
    });

    // --- Parse response safely -------------------------------------------
    let result: any;
    try {
      result = await response.json();
    } catch (parseErr) {
      console.error('Cannot parse e‑mail API response JSON:', parseErr);
      return {
        success: false,
        error: `Server response parsing error: ${response.status} ${response.statusText}`
      };
    }

    console.log('📧 Email API response:', {
      status: response.status,
      result
    });

    if (!response.ok) {
      return {
        success: false,
        error:
          result?.error ||
          `HTTP ${response.status}: ${response.statusText}`
      };
    }

    return {
      success: true,
      message: `Conversation summary successfully sent to ${recipient_email}`,
      timestamp: emailData.timestamp,
      messageId: result.messageId
    };
  } catch (error: unknown) {
    console.error('Error in handleEmailConversation:', error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Network or unknown error occurred while sending e‑mail'
    };
  }
}
