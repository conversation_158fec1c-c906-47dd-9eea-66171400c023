// Function tool definition for Gemini Live API to send conversation summaries via email
import { Type } from '@google/genai';

export const emailConversationTool = {
  name: 'send_conversation_email',
  description: 'Send a conversation summary via email to the user. This function captures the user\'s question and AI response, then emails it to the specified recipient with a professional format.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      recipient_email: {
        type: Type.STRING,
        description: 'The email address where the conversation summary should be sent. Must be a valid email format.',
        pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'
      },
      user_question: {
        type: Type.STRING,
        description: 'The original question or input from the user that initiated this conversation.'
      },
      ai_response: {
        type: Type.STRING,
        description: 'The AI model\'s response to the user\'s question. This should be the complete response provided by <PERSON>.'
      },
      subject: {
        type: Type.STRING,
        description: 'Optional custom subject line for the email. If not provided, a default subject will be used.',
        default: 'Your Our Kidz AI Conversation Summary'
      },
      include_context: {
        type: Type.BOOLEAN,
        description: 'Whether to include additional conversation context in the email.',
        default: false
      },
      conversation_context: {
        type: Type.STRING,
        description: 'Optional additional context about the conversation, such as previous messages or session information.'
      }
    },
    required: ['recipient_email', 'user_question', 'ai_response']
  }
}

// Function to handle the email tool call
export async function handleEmailConversation(parameters: {
  recipient_email: string
  user_question: string
  ai_response: string
  subject?: string
  include_context?: boolean
  conversation_context?: string
}) {
  try {
    // Validate required parameters
    if (!parameters.recipient_email || !parameters.user_question || !parameters.ai_response) {
      return {
        success: false,
        error: 'Missing required parameters: recipient_email, user_question, or ai_response'
      }
    }

    // Add timestamp and session ID
    const emailData = {
      ...parameters,
      timestamp: new Date().toISOString(),
      session_id: `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`
    }

    console.log('Sending email with data:', emailData)

    // Call the email API with absolute URL for client-side fetch
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : ''
    const apiUrl = `${baseUrl}/api/send-conversation`
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailData),
    })

    let result
    try {
      result = await response.json()
    } catch (parseError) {
      console.error('Failed to parse response JSON:', parseError)
      return {
        success: false,
        error: `Server response parsing error: ${response.status} ${response.statusText}`
      }
    }

    console.log('Email API response:', { status: response.status, result })

    if (!response.ok) {
      return {
        success: false,
        error: result?.error || `HTTP ${response.status}: ${response.statusText}`
      }
    }

    return {
      success: true,
      message: `Conversation summary successfully sent to ${parameters.recipient_email}`,
      timestamp: emailData.timestamp,
      messageId: result.messageId
    }

  } catch (error) {
    console.error('Error in handleEmailConversation:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network or unknown error occurred while sending email'
    }
  }
}

// Type definitions for TypeScript
export interface EmailConversationParams {
  recipient_email: string
  user_question: string
  ai_response: string
  subject?: string
  include_context?: boolean
  conversation_context?: string
}

export interface EmailConversationResult {
  success: boolean
  message?: string
  error?: string
  timestamp?: string
  messageId?: string
}
